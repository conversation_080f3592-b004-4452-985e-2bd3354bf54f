const pdfParse = require('pdf-parse');
const axios = require('axios');
const Tesseract = require('tesseract.js');
const asyncHandler = require('express-async-handler');
const Joi = require('joi');

// Configuration from environment variables
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_URL = process.env.OPENROUTER_API_URL || 'https://openrouter.ai/api/v1/chat/completions';

/**
 * Extract text from PDF buffer using pdf-parse
 */
async function extractTextFromPDF(buffer) {
  try {
    const data = await pdfParse(buffer);
    return data.text;
  } catch (error) {
    console.error('PDF parsing error:', error);
    throw new Error('Failed to extract text from PDF');
  }
}

/**
 * Extract text from image using Tesseract.js OCR
 */
async function extractTextFromImage(buffer) {
  try {
    console.log('Starting OCR extraction...');
    const { data: { text } } = await Tesseract.recognize(buffer, 'eng', {
      logger: m => console.log('OCR Progress:', m)
    });
    return text;
  } catch (error) {
    console.error('OCR extraction error:', error);
    throw new Error('Failed to extract text from image');
  }
}

/**
 * Local heuristic extraction using regex patterns
 */
function extractWithHeuristics(text) {
  const result = {
    holderName: '',
    fatherName: '',
    villageName: '',
    district: '',
    area: ''
  };

  // Normalize text for better matching
  const normalizedText = text.replace(/\s+/g, ' ').trim();

  // Extract holder name patterns
  const holderPatterns = [
    /(?:holder|applicant|name)[:\s]*([A-Za-z\s]+?)(?:\n|son|daughter|father|village)/i,
    /(?:श्री|श्रीमती|mr|mrs|ms)[.\s]*([A-Za-z\s]+?)(?:\n|son|daughter|father|village)/i,
    /name[:\s]*([A-Za-z\s]+?)(?:\n|son|daughter|father)/i
  ];

  for (const pattern of holderPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.holderName = match[1].trim();
      break;
    }
  }

  // Extract father's name patterns
  const fatherPatterns = [
    /(?:son|daughter)\s+of[:\s]*([A-Za-z\s]+?)(?:\n|village|district)/i,
    /(?:father|husband)[:\s]*([A-Za-z\s]+?)(?:\n|village|district)/i,
    /s\/o[:\s]*([A-Za-z\s]+?)(?:\n|village|district)/i
  ];

  for (const pattern of fatherPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.fatherName = match[1].trim();
      break;
    }
  }

  // Extract village name patterns
  const villagePatterns = [
    /village[:\s]*([A-Za-z\s]+?)(?:\n|district|tehsil|block)/i,
    /gram[:\s]*([A-Za-z\s]+?)(?:\n|district|tehsil|block)/i
  ];

  for (const pattern of villagePatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.villageName = match[1].trim();
      break;
    }
  }

  // Extract district patterns
  const districtPatterns = [
    /district[:\s]*([A-Za-z\s]+?)(?:\n|state|pin)/i,
    /जिला[:\s]*([A-Za-z\s]+?)(?:\n|state|pin)/i
  ];

  for (const pattern of districtPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1]) {
      result.district = match[1].trim();
      break;
    }
  }

  // Extract area patterns
  const areaPatterns = [
    /(\d+(?:\.\d+)?)\s*(acre|hectare|ha|ac|एकड़)/i,
    /area[:\s]*(\d+(?:\.\d+)?)\s*(acre|hectare|ha|ac|एकड़)/i,
    /क्षेत्रफल[:\s]*(\d+(?:\.\d+)?)\s*(acre|hectare|ha|ac|एकड़)/i
  ];

  for (const pattern of areaPatterns) {
    const match = normalizedText.match(pattern);
    if (match && match[1] && match[2]) {
      result.area = `${match[1]} ${match[2]}`;
      break;
    }
  }

  return result;
}

/**
 * Extract structured data using OpenRouter LLM
 */
async function extractWithLLM(text) {
  if (!OPENROUTER_API_KEY) {
    console.warn('OpenRouter API key not configured, skipping LLM extraction');
    return null;
  }

  try {
    const messages = [
      {
        role: "system",
        content: "You are an expert FRA document extraction assistant. Your task: read the supplied raw document text and return EXACTLY a JSON object (no extra commentary) with keys: holderName, fatherName, villageName, district, area, summary. - If a field is missing, return an empty string for that field. - summary must be a short 2-3 line plain-English summary describing the document (e.g., \"This document is a land claim by X for Y area in Z village, referencing IFR, dated ...\"). Return valid JSON ONLY."
      },
      {
        role: "user",
        content: `RAW_DOCUMENT_TEXT: """${text}"""`
      }
    ];

    const response = await axios.post(OPENROUTER_URL, {
      model: "deepseek/deepseek-chat-v3.1:free",
      messages: messages
    }, {
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    const content = response.data.choices[0].message.content;

    // Try to parse JSON from the response
    let jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    throw new Error('No valid JSON found in LLM response');
  } catch (error) {
    console.error('LLM extraction error:', error.message);
    return null;
  }
}

/**
 * Merge heuristic and LLM results with confidence scoring
 */
function mergeExtractionResults(heuristicResult, llmResult, rawTextLength) {
  const result = {
    holderName: '',
    fatherName: '',
    villageName: '',
    district: '',
    area: '',
    summary: '',
    confidence: 0.0
  };

  let matchCount = 0;
  let totalFields = 5;

  // Merge results, preferring LLM when available, falling back to heuristics
  const fields = ['holderName', 'fatherName', 'villageName', 'district', 'area'];

  for (const field of fields) {
    const heuristicValue = heuristicResult[field] || '';
    const llmValue = llmResult?.[field] || '';

    if (llmValue && heuristicValue) {
      // Both methods found a value
      result[field] = llmValue; // Prefer LLM result
      matchCount += 1;
    } else if (llmValue) {
      // Only LLM found a value
      result[field] = llmValue;
      matchCount += 0.6;
    } else if (heuristicValue) {
      // Only heuristics found a value
      result[field] = heuristicValue;
      matchCount += 0.4;
    }
  }

  // Add summary from LLM if available
  if (llmResult?.summary) {
    result.summary = llmResult.summary;
  } else {
    result.summary = generateFallbackSummary(result);
  }

  // Calculate confidence score
  const baseConfidence = matchCount / totalFields;
  const textQualityBonus = Math.min(rawTextLength / 500, 0.2); // Up to 0.2 bonus for longer text
  result.confidence = Math.min(baseConfidence + textQualityBonus, 1.0);

  return result;
}

/**
 * Generate fallback summary when LLM is not available
 */
function generateFallbackSummary(extractedData) {
  const { holderName, villageName, district, area } = extractedData;

  let summary = "This document appears to be a Forest Rights Act (FRA) claim";

  if (holderName) {
    summary += ` by ${holderName}`;
  }

  if (area) {
    summary += ` for ${area}`;
  }

  if (villageName) {
    summary += ` in ${villageName} village`;
  }

  if (district) {
    summary += `, ${district} district`;
  }

  summary += ". Document processing completed using automated extraction.";

  return summary;
}

/**
 * Main controller function for document extraction
 */
const extractDocumentData = asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      error: 'No file uploaded',
      message: 'Please upload a PDF or image file'
    });
  }

  const { buffer, mimetype, originalname } = req.file;
  let extractedText = '';

  try {
    // Handle PDF files
    if (mimetype === 'application/pdf') {
      console.log('Processing PDF file...');
      extractedText = await extractTextFromPDF(buffer);
    }
    // Handle image files with OCR
    else if (mimetype.startsWith('image/')) {
      console.log('Processing image file with OCR...');
      extractedText = await extractTextFromImage(buffer);
    }
    else {
      return res.status(400).json({
        error: 'Unsupported file type',
        message: 'Please upload a PDF or image file (JPG, PNG, etc.)'
      });
    }

    console.log(`Extracted text length: ${extractedText.length} characters`);

    // Run both extraction methods
    const heuristicResult = extractWithHeuristics(extractedText);
    const llmResult = await extractWithLLM(extractedText);

    // Merge results with confidence scoring
    const finalResult = mergeExtractionResults(heuristicResult, llmResult, extractedText.length);

    // Add metadata and raw text
    const response = {
      ...finalResult,
      rawText: extractedText,
      metadata: {
        originalFilename: originalname,
        fileType: mimetype,
        extractedTextLength: extractedText.length,
        extractionTimestamp: new Date().toISOString(),
        heuristicExtraction: heuristicResult,
        llmExtraction: llmResult
      }
    };

    res.json({
      success: true,
      data: response,
      message: 'Document processed successfully'
    });

  } catch (error) {
    console.error('Document extraction error:', error);

    res.status(500).json({
      error: 'Document extraction failed',
      message: error.message,
      rawText: extractedText || '',
      fallbackData: {
        holderName: '',
        fatherName: '',
        villageName: '',
        district: '',
        area: '',
        summary: 'Document processing failed. Please try again or enter details manually.',
        confidence: 0.0
      }
    });
  }
});

/**
 * Debug endpoint for NER-only extraction
 */
const extractNEROnly = asyncHandler(async (req, res) => {
  const { text } = req.body;

  if (!text) {
    return res.status(400).json({
      error: 'No text provided',
      message: 'Please provide text in the request body'
    });
  }

  try {
    const llmResult = await extractWithLLM(text);

    res.json({
      success: true,
      data: llmResult,
      message: 'NER extraction completed'
    });
  } catch (error) {
    console.error('NER extraction error:', error);
    res.status(500).json({
      error: 'NER extraction failed',
      message: error.message
    });
  }
});

module.exports = {
  extractDocumentData,
  extractNEROnly,
  extractTextFromPDF,
  extractTextFromImage,
  extractWithHeuristics,
  extractWithLLM,
  mergeExtractionResults
};
